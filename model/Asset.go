package model

import "github.com/uptrace/bun"

type Asset struct {
	bun.BaseModel `bun:"deployment.asset"`
	BaseEntityModel
	AssetId               int64 `bun:",notnull,unique"`
	SysInfo               string
	AntiMalwareVersion    string `bun:"type:varchar(100)"`
	SpywareVersion        string `bun:"type:varchar(100)"`
	MrtVersion            string `bun:"type:varchar(100)"`
	InstalledKbList       []string
	LastPatchScanTime     int64
	DisplayVersion        string `bun:"type:varchar(100)"`
	InstalledSoftwareList []AssetInstallPkgMetadata
	OfficeData            string `bun:"type:text"`
}

type AssetInstallPkgMetadata struct {
	Name    string `json:"Name"`
	Version string `json:"Version"`
}
