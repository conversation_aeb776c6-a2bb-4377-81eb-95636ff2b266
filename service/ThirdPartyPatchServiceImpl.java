package com.flotomate.fs.patch.thirdpartpatch;

import com.flotomate.common.agent.ApplicationType;
import com.flotomate.common.agent.PatchIdRest;
import com.flotomate.common.agent.ScannedPatchDataRest;
import com.flotomate.common.rest.asset.OsArchitecture;
import com.flotomate.common.rest.asset.OsPlatform;
import com.flotomate.common.rest.flotofile.FlotoFileType;
import com.flotomate.common.rest.patch.AgentPatchRest;
import com.flotomate.common.rest.patch.CentralRepoAPIsConstants;
import com.flotomate.common.rest.patch.OfficePatchDownloadResponse;
import com.flotomate.common.rest.patch.PatchConstants;
import com.flotomate.common.rest.patch.PatchRest;
import com.flotomate.common.rest.patch.PatchState;
import com.flotomate.common.rest.patch.PatchType;
import com.flotomate.common.rest.patch.Version;
import com.flotomate.common.rest.patch.msrc.LatestPatchResponse;
import com.flotomate.common.rest.patch.thirdparty.ThirdPartyPatchRest;
import com.flotomate.common.rest.qual.RelationalOperator;
import com.flotomate.common.utils.FlotoStringUtils;
import com.flotomate.fs.patch.agentpatch.AgentPatchService;
import com.flotomate.fs.patch.datadump.PatchDumpService;
import com.flotomate.fs.patch.declinepatchconfig.DeclinePatchConfigService;
import com.flotomate.fs.patch.gateway.MainServerToCentralPatchGatewayService;
import com.flotomate.fs.patch.patchbase.PatchBaseServiceImpl;
import com.flotomate.fs.patch.service.PatchService;
import com.flotomate.fs.patch.service.patchlanguage.PatchLangauageService;
import com.flotomate.fs.patch.thirdpartpatch.application.ThirdPartyApplicationRepository;
import com.flotomate.model.agent.Agent;
import com.flotomate.model.agent.AgentApplicationConfig;
import com.flotomate.model.command.Command;
import com.flotomate.model.command.Command_;
import com.flotomate.models.context.CallContext;
import com.flotomate.models.patch.FileDetails;
import com.flotomate.models.patch.OsApplication;
import com.flotomate.models.patch.Patch;
import com.flotomate.models.patch.PatchBase_;
import com.flotomate.models.patch.PatchDbSettings;
import com.flotomate.models.patch.thirdpartypatch.ThirdPartyApplication;
import com.flotomate.models.proxy.ProxyServer;
import com.flotomate.service.agent.AgentService;
import com.flotomate.service.agent.application.AgentApplicationConfigService;
import com.flotomate.service.base.reference.FlotoPartLocator;
import com.flotomate.service.command.service.CommandService;
import com.flotomate.service.file.FileStorageService;
import com.flotomate.service.utility.QualUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class ThirdPartyPatchServiceImpl extends PatchBaseServiceImpl<ThirdPartyPatch, ThirdPartyPatchRest>
        implements ThirdPartyPatchService {

    ThirdPartyApplicationRepository thirdPartyApplicationRepository;

    ThirdPartyPatchServiceImpl selfProxy;

    AgentApplicationConfigService agentApplicationConfigService;

    FileStorageService fileStorageService;

    CommandService commandService;

    public ThirdPartyPatchServiceImpl(ThirdPartyPatchRepository repository,
            PatchLangauageService patchLangauageService,
            MainServerToCentralPatchGatewayService mainServerToCPServer, PatchDumpService patchDumpService,
            AgentService agentService, AgentPatchService agentPatchService,
            DeclinePatchConfigService declineService,
            ThirdPartyApplicationRepository thirdPartyApplicationRepository,
            AgentApplicationConfigService agentApplicationConfigService,
            FileStorageService fileStorageService, CommandService commandService, PatchService patchService) {
        super(repository, ThirdPartyPatch.class, patchLangauageService, mainServerToCPServer,
                patchDumpService, agentService, agentPatchService, declineService);
        this.thirdPartyApplicationRepository = thirdPartyApplicationRepository;
        this.agentApplicationConfigService = agentApplicationConfigService;
        this.fileStorageService = fileStorageService;
        this.commandService = commandService;
        this.patchService = patchService;
    }

    @PostConstruct
    public void init() {
        selfProxy = appContext.getBean(ThirdPartyPatchServiceImpl.class);
    }

    @Override
    protected ThirdPartyPatch convertToDomain(CallContext callContext, ThirdPartyPatchRest createRequest) {
        ThirdPartyPatch domain = super.convertToDomain(callContext, createRequest);
        domain.setInstallCommand(createRequest.getInstallCommand());
        domain.setUnInstallCommand(createRequest.getUnInstallCommand());
        domain.setProductCode(createRequest.getProductCode());
        domain.setVersion(createRequest.getVersion());
        domain.setFolderName(createRequest.getFolderName());
        domain.setPatchData(createRequest.getPatchData());
        return domain;
    }

    @Override
    protected ThirdPartyPatch getDomainModel(ThirdPartyPatchRest createRequest) {
        return new ThirdPartyPatch();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void processThirdPartyPatchData(CallContext callContext, Agent agent,
            ScannedPatchDataRest scannedPatchDataRest) {
        List<AgentPatchRest> patchList =
                getAgentPatchListForThirdPartyPatchList(callContext, agent, scannedPatchDataRest);
        serviceLogger.debug("Total ThirdParty PatchList found for Agent Id:{}., --> {}", agent.getId(),
                CollectionUtils.size(patchList));

        processAgentPatchList(callContext, agent, patchList, PatchType.THIRD_PARTY);
        serviceLogger.debug("Process Thirdparty patch Data Done...!");
    }

    public List<AgentPatchRest> getAgentPatchListForThirdPartyPatchList(CallContext callContext, Agent agent,
            ScannedPatchDataRest scannedPatchDataRest) {
        List<AgentPatchRest> agentPatchList = new ArrayList<>();
        serviceLogger.debug("---------- Started ThirdParty Proccess------");
        serviceLogger.debug("Getting Agent Patch Data for ThirdParty For agent - {} ", agent.getId());
        if (CollectionUtils.isNotEmpty(scannedPatchDataRest.getInstalledPatches())) {
            Set<PatchIdRest> installedPatches = new HashSet<>(scannedPatchDataRest.getInstalledPatches());
            if (CollectionUtils.isNotEmpty(installedPatches)) {
                processThirdPartyPatchList(callContext, agent, installedPatches, agentPatchList,
                        PatchState.INSTALLED);
            }
        }

        if (CollectionUtils.isNotEmpty(scannedPatchDataRest.getMissingPatches())) {
            Set<PatchIdRest> missingPatchList = new HashSet<>(scannedPatchDataRest.getMissingPatches());
            if (CollectionUtils.isNotEmpty(missingPatchList)) {
                processThirdPartyPatchList(callContext, agent, missingPatchList, agentPatchList,
                        PatchState.MISSING);
            }
        }
        processOfficePatch(callContext, agent, agentPatchList);

        serviceLogger.debug("Total patch discovered : {}", agentPatchList.size());
        serviceLogger.debug("---------- Completed Thirdparty Patch Proccess------");
        return agentPatchList;
    }

    /**
     * Processes Office patches for the given agent and adds them to the agent patch list.
     * This method identifies applicable Office patches based on the agent's configuration
     * and categorizes them as either installed or missing.
     *
     * @param callContext    The call context for the operation
     * @param agent          The agent for which to process Office patches
     * @param agentPatchList The list to which discovered patches will be added
     */
    private void processOfficePatch(CallContext callContext, Agent agent,
            List<AgentPatchRest> agentPatchList) {
        serviceLogger.info("Starting Office patch processing for agent: {}", agent.getId());

        // Check if Microsoft Office patching is enabled
        if (!isOfficePatchingEnabled(callContext)) {
            serviceLogger.info("Microsoft Office third-party application is not enabled for patching");
            return;
        }

        // Get agent's Office application configuration
        AgentApplicationConfig agentAppConfig = getAgentOfficeConfiguration(callContext, agent.getId());
        if (agentAppConfig == null) {
            serviceLogger.info("No valid Office application configuration found for agent: {}",
                    agent.getId());
            return;
        }

        // Extract update channel and version from agent configuration
        String updateChannel = getConfigValue(agentAppConfig, "updatechannel");
        if (FlotoStringUtils.isBlank(updateChannel)) {
            serviceLogger.info("No update channel found in Office configuration for agent: {}",
                    agent.getId());
            return;
        }

        String version = getConfigValue(agentAppConfig, "version");
        if (FlotoStringUtils.isBlank(version)) {
            serviceLogger.info("No version found in Office configuration for agent: {}", agent.getId());
            return;
        }

        // Find applicable patches for the update channel
        List<ThirdPartyPatch> thirdPartyPatches = findPatchesByUpdateChannel(callContext, updateChannel);
        if (CollectionUtils.isEmpty(thirdPartyPatches)) {
            serviceLogger.info("No Office patches found for update channel: {}", updateChannel);
            return;
        }

        // Evaluate patches and determine their states (missing or installed)
        PatchVersionEvaluationResult evaluationResult =
                evaluatePatchVersionStates(thirdPartyPatches, updateChannel, version);

        // Process each patch and add to agent patch list
        processPatchesForAgent(callContext, agent, agentAppConfig, evaluationResult, agentPatchList);

        serviceLogger.info("Completed Office patch processing for agent: {}", agent.getId());
    }

    /**
     * Checks if Microsoft Office patching is enabled in the system settings.
     *
     * @param callContext The call context for the operation
     * @return true if Office patching is enabled, false otherwise
     */
    private boolean isOfficePatchingEnabled(CallContext callContext) {
        ThirdPartyApplication thirdPartyApplication =
                thirdPartyApplicationRepository.getByName(callContext, PatchConstants.MICROSOFT_OFFICE);
        PatchDbSettings patchDbSettings = patchDbSettingsService.get(callContext);

        return thirdPartyApplication != null && patchDbSettings.isThirdPartyPatchEnabled()
                && patchDbSettings.getThirdPartyApplications().contains(thirdPartyApplication.getUuid());
    }

    /**
     * Retrieves the Office application configuration for the specified agent.
     *
     * @param callContext The call context for the operation
     * @param agentId     The ID of the agent
     * @return The agent's Office application configuration, or null if not found or invalid
     */
    private AgentApplicationConfig getAgentOfficeConfiguration(CallContext callContext, long agentId) {
        AgentApplicationConfig config =
                agentApplicationConfigService.getOneByReference(callContext, new FlotoPartLocator(agentId),
                        false);

        if (config != null && ApplicationType.MICROSOFT_OFFICE == config.getApplicationType()
                && MapUtils.isNotEmpty(config.getApplicationData())) {
            serviceLogger.info("Found valid Office application configuration for agent: {}", agentId);
            return config;
        }

        return null;
    }

    /**
     * Safely extracts a string value from the agent application configuration.
     *
     * @param config The agent application configuration
     * @param key    The key for the value to extract
     * @return The extracted value, or null if not found
     */
    private String getConfigValue(AgentApplicationConfig config, String key) {
        if (config.getApplicationData().containsKey(key)) {
            String value = (String) config.getApplicationData().get(key);
            serviceLogger.debug("Found {} for Office: {}", key, value);
            return value;
        }
        return null;
    }

    /**
     * Finds patches that match the specified update channel.
     *
     * @param callContext   The call context for the operation
     * @param updateChannel The update channel to search for
     * @return A list of matching patches
     */
    private List<ThirdPartyPatch> findPatchesByUpdateChannel(CallContext callContext, String updateChannel) {
        serviceLogger.info("Searching for Office patches with update channel: {}", updateChannel);
        return searchByQualification(callContext,
                QualUtils.buildRelationalQual(ThirdPartyPatch_.PRODUCT_CODE, RelationalOperator.Equal,
                        updateChannel), null);
    }

    /**
     * Processes the evaluated patches for the agent and adds them to the agent patch list.
     *
     * @param callContext      The call context for the operation
     * @param agent            The agent for which to process patches
     * @param agentAppConfig   The agent's application configuration
     * @param evaluationResult The result of patch evaluation
     * @param agentPatchList   The list to which discovered patches will be added
     */
    private void processPatchesForAgent(CallContext callContext, Agent agent,
            AgentApplicationConfig agentAppConfig, PatchVersionEvaluationResult evaluationResult,
            List<AgentPatchRest> agentPatchList) {

        for (ThirdPartyPatch thirdPartyPatch : evaluationResult.allPatches()) {
            // Generate Office configuration XML
            String configXml = prepareOfficeConfigurationXml(agentAppConfig.getApplicationData(),
                    thirdPartyPatch.getVersion());
            serviceLogger.debug("Generated Office configuration XML: {}", configXml);

            // Create or get patch from third-party patch
            Patch patch = selfProxy.createFromThirdPartyPatch(callContext, thirdPartyPatch, agent,
                    ApplicationType.MICROSOFT_OFFICE);
            if (patch != null) {
                serviceLogger.warn("Failed to create patch from third-party patch: {}",
                        thirdPartyPatch.getUuid());
                // Set patch state based on evaluation result
                PatchState patchState = evaluationResult.uuidByStateMap().get(patch.getUuid());
                if (patchState == null) {
                    serviceLogger.warn("No patch state found for patch: {}", patch.getUuid());
                    continue;
                }

                // Save patch with XML configuration
                serviceLogger.info("Setting XML configuration for patch ID: {}", patch.getId());
                patch.setXmlFormat(configXml);
                patchService.forceSave(callContext, patch);

                // Create and add agent patch rest object
                AgentPatchRest rest = createAgentPatchRest(patch.getId(), agent.getId(), patchState);
                agentPatchList.add(rest);

                serviceLogger.info(
                        "Added Office patch with ID: {} for agent: {} with state: {} (version: {})",
                        patch.getId(), agent.getId(), patchState, thirdPartyPatch.getVersion());
            }

        }
    }

    /**
     * Creates an AgentPatchRest object with the specified properties.
     *
     * @param patchId    The ID of the patch
     * @param agentId    The ID of the agent
     * @param patchState The state of the patch
     * @return The created AgentPatchRest object
     */
    private AgentPatchRest createAgentPatchRest(long patchId, long agentId, PatchState patchState) {
        AgentPatchRest rest = new AgentPatchRest();
        rest.setPatchId(patchId);
        rest.setAgentId(agentId);
        rest.setPatchState(patchState);
        rest.setPatchType(PatchType.THIRD_PARTY);
        return rest;
    }

    /**
     * Evaluates patch versions to determine which patches are missing and which are installed.
     * For missing patches, only the highest version patch is included in the result.
     *
     * @param thirdPartyPatches The list of patches to evaluate
     * @param updateChannel     The update channel for logging purposes
     * @param agentVersion      The current version installed on the agent
     * @return A result object containing the evaluation results
     */
    private PatchVersionEvaluationResult evaluatePatchVersionStates(List<ThirdPartyPatch> thirdPartyPatches,
            String updateChannel, String agentVersion) {
        Map<String, PatchState> uuidByStateMap = new HashMap<>();
        serviceLogger.info("Found {} Third Party Office patches for update channel: {}",
                thirdPartyPatches.size(), updateChannel);

        // Categorize patches based on version comparison
        List<ThirdPartyPatch> missingPatches = new ArrayList<>();
        List<ThirdPartyPatch> installedPatches = new ArrayList<>();

        categorizePatchesByVersion(thirdPartyPatches, agentVersion, missingPatches, installedPatches);

        serviceLogger.info("Categorized patches - Missing: {}, Installed: {}", missingPatches.size(),
                installedPatches.size());

        // Process all patches (both missing and installed)
        List<ThirdPartyPatch> allPatches = new ArrayList<>(installedPatches);

        // Map installed patches to their state
        if (CollectionUtils.isNotEmpty(installedPatches)) {
            uuidByStateMap = installedPatches.stream()
                    .collect(Collectors.toMap(ThirdPartyPatch::getUuid, p -> PatchState.INSTALLED));
            serviceLogger.debug("Installed patch map size: {}", uuidByStateMap.size());
        }

        // For missing patches, only include the highest version patch
        if (CollectionUtils.isNotEmpty(missingPatches)) {
            ThirdPartyPatch highestVersionPatch = findPatchWithHighestVersion(missingPatches);
            if (highestVersionPatch != null) {
                allPatches.add(highestVersionPatch);
                uuidByStateMap.put(highestVersionPatch.getUuid(), PatchState.MISSING);
                serviceLogger.info(
                        "Highest version patch in missing patches - UUID: {}, Version: {}, Name: {}",
                        highestVersionPatch.getUuid(), highestVersionPatch.getVersion(),
                        highestVersionPatch.getName());
            }
        }

        return new PatchVersionEvaluationResult(uuidByStateMap, allPatches);
    }

    /**
     * Categorizes patches into missing and installed lists based on version comparison.
     *
     * @param patches          The patches to categorize
     * @param agentVersion     The current version installed on the agent
     * @param missingPatches   The list to which missing patches will be added
     * @param installedPatches The list to which installed patches will be added
     */
    private void categorizePatchesByVersion(List<ThirdPartyPatch> patches, String agentVersion,
            List<ThirdPartyPatch> missingPatches, List<ThirdPartyPatch> installedPatches) {

        for (ThirdPartyPatch patch : patches) {
            if (patch.getVersion() == null) {
                serviceLogger.warn("Skipping patch with null version: {}", patch.getUuid());
                continue;
            }

            try {
                if (isIncomingVersionIsIncompatible(patch.getVersion(), agentVersion)) {
                    // Agent version is lower than patch version - patch is needed
                    missingPatches.add(patch);
                } else {
                    // Agent version is equal to or higher than patch version - patch is already installed
                    installedPatches.add(patch);
                }
            } catch (Exception e) {
                serviceLogger.error("Error comparing versions for patch {}: {}", patch.getUuid(),
                        e.getMessage());
            }
        }
    }

    /**
     * Result class for patch version evaluation containing the state map and list of patches.
     */
    private record PatchVersionEvaluationResult(Map<String, PatchState> uuidByStateMap,
                                                List<ThirdPartyPatch> allPatches) {
    }

    /**
     * Prepares Office configuration XML from application data
     *
     * @param applicationData Map containing Office application configuration data
     * @return XML configuration string in the format required by Office Deployment Tool
     */
    private String prepareOfficeConfigurationXml(Map<String, Object> applicationData, String version) {
        if (MapUtils.isEmpty(applicationData)) {
            serviceLogger.warn("Application data is empty, cannot generate Office configuration XML");
            return null;
        }
        String xmlFormat = """
                <Configuration>
                  <Add OfficeClientEdition="{platform}" Version="{version}">
                    <Product ID="{releaseid}">
                      <Language ID="{language}" />
                      {excludedapps}
                    </Product>
                  </Add>
                  <Display Level="None" AcceptEULA="TRUE" />
                </Configuration>
                """;

        // Extract required values from applicationData
        String platform = applicationData.containsKey("platform") ?
                String.valueOf(applicationData.get("platform")).replace("X", "") :
                "64"; // Default to 64-bit
        xmlFormat = xmlFormat.replace("{platform}", platform);
        String productReleaseIds = applicationData.containsKey("productreleaseids") ?
                String.valueOf(applicationData.get("productreleaseids")) :
                "O365ProPlusRetail";
        xmlFormat = xmlFormat.replace("{releaseid}", productReleaseIds);
        String language = applicationData.containsKey("language") ?
                String.valueOf(applicationData.get("language")) :
                "en-us";
        xmlFormat = xmlFormat.replace("{language}", language);
        xmlFormat = xmlFormat.replace("{version}", version);
        StringBuilder xmlBuilder = new StringBuilder(100);

        // Add ExcludedApps if present
        if (applicationData.containsKey("excludedapps")) {
            Object excludedAppsObj = applicationData.get("excludedapps");
            if (excludedAppsObj instanceof List) {
                List<?> excludedApps = (List<?>) excludedAppsObj;
                for (Object app : excludedApps) {
                    xmlBuilder.append("      <ExcludeApp ID=\"").append(app).append("\" />\n");
                }
            } else if (excludedAppsObj instanceof String excludedAppsStr) {
                // Handle case where excludedapps might be a comma-separated string
                String[] apps = excludedAppsStr.split(",");
                for (String app : apps) {
                    if (!app.trim().isEmpty()) {
                        xmlBuilder.append("      <ExcludeApp ID=\"").append(app.trim()).append("\" />\n");
                    }
                }
            }
        }
        xmlFormat = xmlFormat.replace("{excludedapps}", xmlBuilder.toString());
        return xmlFormat;
    }

    /**
     * Determines if the agent's version is incompatible with the patch version.
     *
     * @param patchVersion The version of the patch being evaluated
     * @param agentVersion The version currently installed on the agent
     * @return true if the agent's version is less than the patch version (needs update),
     * false if the agent's version is greater than or equal to the patch version (already updated)
     */
    public boolean isIncomingVersionIsIncompatible(String patchVersion, String agentVersion) {
        boolean isAgentVersionHigherOrEqual = false;
        try {
            Version agentVer = new Version(agentVersion);
            isAgentVersionHigherOrEqual = agentVer.compareTo(new Version(patchVersion)) < 0;
        } catch (Exception e) {
            serviceLogger.error("Exception while comparing versions: {} and {}", patchVersion, agentVersion,
                    e);
        }
        return isAgentVersionHigherOrEqual;
    }

    private void processThirdPartyPatchList(CallContext callContext, Agent agent,
            Set<PatchIdRest> installedPatches, List<AgentPatchRest> agentPatchList, PatchState patchState) {

        List<String> patchUuidList =
                installedPatches.stream().map(PatchIdRest::getPatchUuid).collect(Collectors.toList());

        List<ThirdPartyPatch> thirdPartyPatches = searchByQualification(callContext,
                QualUtils.buildRelationalQual(PatchBase_.UUID, RelationalOperator.In, patchUuidList), null);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(thirdPartyPatches)) {
            serviceLogger.debug("Found Third Party total {} {} patch", thirdPartyPatches.size(), patchState);
            for (ThirdPartyPatch thirdPartyPatchReleasePackage : thirdPartyPatches) {
                Patch patch =
                        selfProxy.createFromThirdPartyPatch(callContext, thirdPartyPatchReleasePackage, agent,
                                null);
                if (patch != null) {
                    AgentPatchRest rest = new AgentPatchRest();
                    rest.setPatchId(patch.getId());
                    rest.setAgentId(agent.getId());
                    rest.setPatchState(patchState);
                    rest.setPatchType(PatchType.THIRD_PARTY);
                    agentPatchList.add(rest);

                }
            }
        } else {
            serviceLogger.debug("No data found while processing Third party {} patch list", patchState);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Patch createFromThirdPartyPatch(CallContext callContext, ThirdPartyPatch thirdPartyPatch,
            Agent agent, ApplicationType applicationType) {

        Patch patchObj = patchService.getByUuid(callContext, thirdPartyPatch.getUuid());

        if (patchObj == null) {
            PatchRest rest = createFromPatchBase(callContext, thirdPartyPatch);
            rest.setPlatform(agent.getPlatform());
            rest.setArch(agent.getArchitecture());
            rest.setPatchType(PatchType.THIRD_PARTY);
            rest.setApplicationType(applicationType);

            Set<Long> affectedOsAppId = new HashSet<>();
            OsApplication app = osApplicationService.createOrGetApplicationForThirdPartyPatch(callContext,
                    thirdPartyPatch.getName(), agent.getPlatform());
            if (app != null) {
                affectedOsAppId.add(app.getId());
            }
            rest.setInstallCommand(thirdPartyPatch.getInstallCommand());
            rest.setUnInstallCommand(thirdPartyPatch.getUnInstallCommand());
            rest.setAffectedProducts(affectedOsAppId);
            patchObj = createOrGetPatch(callContext, rest);
            serviceLogger.debug("Created Thirdparty patch id : {}", patchObj.getId());

        }
        return patchObj;
    }

    @Override
    public void poolLatestDataFromCentralPatch(CallContext callContext, ProxyServer proxyServer,
            String apiPrefix, boolean downloadMirrorFiles) {
        super.poolLatestDataFromCentralPatch(callContext, proxyServer, apiPrefix, downloadMirrorFiles);
        verifyApplicationData(callContext);
        downloadODTFile(callContext, proxyServer);
    }

    private void downloadODTFile(CallContext callContext, ProxyServer proxyServer) {
        try {
            serviceLogger.info("Downloading ODT file from central patch");
            String fileName = PatchConstants.MICROSOFT_OFFICE_ODT_FILE;
            String fileDownloadUrl =
                    CentralRepoAPIsConstants.API_TO_DOWNLOAD_ALL_CATEGORY_FILE_FOR_WINDOWS + "?filename="
                            + URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            File odtFolderPath =
                    fileStorageService.getPatchDbByCategoryName(callContext, FlotoFileType.PATCHDB,
                            PatchConstants.WINDOWSODT);
            if (odtFolderPath.exists() && odtFolderPath.isDirectory()) {
                FileUtils.cleanDirectory(odtFolderPath);
            } else {
                FileUtils.forceMkdir(odtFolderPath);
            }
            mainServerToCPServer.getPatchDbForCategory(callContext, fileName, fileDownloadUrl,
                    FlotoFileType.PATCHDB, proxyServer, PatchConstants.WINDOWSODT);
        } catch (Exception e) {
            serviceLogger.error("Error while downloading ODT file ", e);
        }
    }

    @Override
    protected List<ThirdPartyPatchRest> getDataFromResponse(CallContext callContext,
            LatestPatchResponse response) {
        return response.getThirdPartyPatchRestList();
    }

    @Override
    public void verifyApplicationData(CallContext callContext) {

        List<ThirdPartyPatch> applicationNamesList = searchByQualification(callContext, null, null);
        if (CollectionUtils.isNotEmpty(applicationNamesList)) {
            for (ThirdPartyPatch thirdPartyPatch : applicationNamesList) {
                serviceLogger.debug("Verifying ThirdParty Application Data for {}", thirdPartyPatch);
                ThirdPartyApplication thirdPartyApplication =
                        thirdPartyApplicationRepository.getByName(callContext,
                                thirdPartyPatch.getName().trim());
                if (thirdPartyApplication == null) {
                    thirdPartyApplication = new ThirdPartyApplication();
                    thirdPartyApplication.setUuid(UUID.randomUUID().toString());
                }
                thirdPartyApplication.setName(thirdPartyPatch.getName().trim());
                if (FlotoStringUtils.isNotBlank(thirdPartyPatch.getFolderName())) {
                    thirdPartyApplication.setFolderName(thirdPartyPatch.getFolderName().trim());
                }
                thirdPartyApplicationRepository.save(callContext, thirdPartyApplication);
                serviceLogger.debug("Updated ThirdParty Application Data for {}", thirdPartyPatch);
            }
        }
    }

    @Override
    public File getOdtFile(CallContext callContext, String fileName) {
        serviceLogger.info("Getting ODT file from path for fileName: {}", fileName);
        String patchOdtPath = fileStorageService.buildFilePathForLocal(callContext, FlotoFileType.PATCHDB,
                PatchConstants.WINDOWSODT);
        if (FlotoStringUtils.isNotBlank(patchOdtPath)) {
            serviceLogger.debug("ODT directory path: {}", patchOdtPath);
            File odtDirectory = new File(patchOdtPath);
            if (odtDirectory.exists() && odtDirectory.isDirectory()) {
                // Find the first .exe file in the directory
                File[] exeFiles = odtDirectory.listFiles((dir, name) -> name.toLowerCase().endsWith(".exe"));
                if (exeFiles != null && exeFiles.length > 0) {
                    serviceLogger.debug("Found .exe file: {}", exeFiles[0].getName());
                    return exeFiles[0];
                } else {
                    serviceLogger.warn("No .exe files found in ODT directory: {}", patchOdtPath);
                }
            } else {
                serviceLogger.warn("ODT directory does not exist or is not a directory: {}", patchOdtPath);
            }
        } else {
            serviceLogger.warn("Invalid ODT directory path");
        }
        return null;
    }


    @Override
    public void updateFileDataByConfigurationId(CallContext callContext, long configurationDownloadStatusId,
            OfficePatchDownloadResponse officePatchDownloadResponse) {
        serviceLogger.info("Updating File Data for Configuration Id : {}", configurationDownloadStatusId);
        List<Command> configCommandList = commandService.searchByQualification(callContext,
                QualUtils.buildRelationalQual(Command_.CONFIG_DOWNLOAD_STATUS_ID, RelationalOperator.Equal,
                        configurationDownloadStatusId), null);

        if (CollectionUtils.isNotEmpty(configCommandList)) {
            serviceLogger.debug("Found total {} command to update file data", configCommandList.size());
            configCommandList.forEach(command -> {
                Patch patch = patchService.getById(callContext, command.getConfigId(), true);
                if (patch != null) {
                    serviceLogger.debug("Updating File Data for Patch Id : {}", patch.getId());

                    String installCommand = String.format("\"%s\" /configure \"%s\"",
                            officePatchDownloadResponse.getOdtFileName(),
                            officePatchDownloadResponse.getXmlFileName());

                    FileDetails fileDetails = new FileDetails();
                    fileDetails.setFileName("install.zip");
                    fileDetails.setArch(OsArchitecture.ALL);
                    fileDetails.setLangCode(1033);
                    fileDetails.setSize(officePatchDownloadResponse.getPatchSize());
                    patch.setInstallCommand(installCommand);
                    patch.setDownloadFileDetails(Set.of(fileDetails));
                    patch.setDownloadSize(officePatchDownloadResponse.getPatchSize());
                    patchService.forceSave(callContext, patch);
                    serviceLogger.debug("Updated File Data for Patch Id : {}", patch.getId());
                }
            });
        }
    }

    @Override
    protected OsPlatform getOsPlatForm() {
        return OsPlatform.THIRD_PARTY_APPLICATION;
    }

    @Override
    public String getXmlContentByPatchName(CallContext callContext, String patchId) {
        serviceLogger.info("Getting XML content for patch ID: {}", patchId);
        Patch patch = patchService.getByName(callContext, patchId);

        if (patch == null) {
            serviceLogger.warn("Patch not found with ID: {}", patchId);
            return null;
        }

        String xmlContent = patch.getXmlFormat();
        if (FlotoStringUtils.isBlank(xmlContent)) {
            serviceLogger.warn("No XML content found for patch ID: {}", patchId);
            return null;
        }

        serviceLogger.debug("Retrieved XML content for patch ID: {}", patchId);
        return xmlContent;
    }

    /**
     * Finds the patch with the highest version from a list of patches.
     *
     * @param patches List of ThirdPartyPatch objects to compare
     * @return The ThirdPartyPatch with the highest version, or null if the list is empty
     */
    private ThirdPartyPatch findPatchWithHighestVersion(List<ThirdPartyPatch> patches) {
        if (CollectionUtils.isEmpty(patches)) {
            return null;
        }

        ThirdPartyPatch highestVersionPatch = patches.get(0);

        for (ThirdPartyPatch patch : patches) {
            if (patch.getVersion() == null) {
                continue; // Skip patches with null version
            }

            if (highestVersionPatch.getVersion() == null) {
                highestVersionPatch = patch; // Replace null version with non-null
                continue;
            }

            try {
                Version currentVersion = new Version(patch.getVersion());
                Version highestVersion = new Version(highestVersionPatch.getVersion());

                if (currentVersion.compareTo(highestVersion) > 0) {
                    highestVersionPatch = patch;
                }
            } catch (Exception e) {
                serviceLogger.warn("Error comparing versions: {} and {}", patch.getVersion(),
                        highestVersionPatch.getVersion(), e);
            }
        }

        return highestVersionPatch;
    }
}
