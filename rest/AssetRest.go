package rest

import "deployment/model"

type AssetInstallPkgMetadataRest struct {
	Name    string `json:"Name"`
	Version string `json:"Version"`
}

type AssetRest struct {
	BaseEntityRest
	AssetId               int64                         `json:"assetId"`
	SysInfo               string                        `json:"sysInfo"`
	AntiMalwareVersion    string                        `json:"antiMalwareVersion"`
	SpywareVersion        string                        `json:"spywareVersion"`
	MrtVersion            string                        `json:"mrtVersion"`
	InstalledKbList       []string                      `json:"installedKbList"`
	LastPatchScanTime     int64                         `json:"lastPatchScanTime"`
	DisplayVersion        string                        `json:"displayVersion"`
	InstalledSoftwareList []AssetInstallPkgMetadataRest `json:"installedSoftwareList"`
	OfficeData            string                        `json:"officeData"`
}

func ConvertToAssetInstallPkgMetadataRestList(dataList []model.AssetInstallPkgMetadata) []AssetInstallPkgMetadataRest {
	var softList []AssetInstallPkgMetadataRest
	for _, data := range dataList {
		softList = append(softList, AssetInstallPkgMetadataRest{
			Name:    data.Name,
			Version: data.Version,
		})
	}
	return softList
}

func ConvertToAssetInstallPkgMetadataList(dataList []AssetInstallPkgMetadataRest) []model.AssetInstallPkgMetadata {
	var softList []model.AssetInstallPkgMetadata
	for _, data := range dataList {
		softList = append(softList, model.AssetInstallPkgMetadata{
			Name:    data.Name,
			Version: data.Version,
		})
	}
	return softList
}
